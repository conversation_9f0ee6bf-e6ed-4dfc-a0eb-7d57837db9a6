package task

import (
	"testing"

	"github.com/Shopify/sarama"
	"github.com/stretchr/testify/assert"
)

func TestBatchOffsetTracker(t *testing.T) {
	// 测试简化版本的批次offset跟踪功能
	tracker := NewBatchOffsetTracker()

	// 创建测试消息
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 1, Offset: 200}

	batch1 := []*sarama.ConsumerMessage{msg1, msg2}
	batch2 := []*sarama.ConsumerMessage{msg3}

	// 添加批次（简化版本）
	tracker.AddBatch(batch1)
	tracker.AddBatch(batch2)

	// 简化版本：CanCommitBatch总是返回true
	assert.True(t, tracker.CanCommitBatch(batch1))
	assert.True(t, tracker.CanCommitBatch(batch2))

	// 测试RemoveBatch（简化版本）
	tracker.RemoveBatch(batch1)
	tracker.RemoveBatch(batch2)
}

func TestBatchOffsetTrackerSequentialCommit(t *testing.T) {
	// 测试简化版本的顺序提交逻辑
	tracker := NewBatchOffsetTracker()

	// 创建有序的消息批次
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 102}

	batch1 := []*sarama.ConsumerMessage{msg1}
	batch2 := []*sarama.ConsumerMessage{msg2}
	batch3 := []*sarama.ConsumerMessage{msg3}

	// 添加所有批次
	tracker.AddBatch(batch1)
	tracker.AddBatch(batch2)
	tracker.AddBatch(batch3)

	// 简化版本：所有批次都可以提交
	assert.True(t, tracker.CanCommitBatch(batch1))
	assert.True(t, tracker.CanCommitBatch(batch2))
	assert.True(t, tracker.CanCommitBatch(batch3))
}

func TestBatchOffsetTrackerSelfExclusion(t *testing.T) {
	// 测试简化版本的批次处理逻辑
	tracker := NewBatchOffsetTracker()

	// 创建一个批次包含多个offset
	msg1 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 100}
	msg2 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 101}
	msg3 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 102}

	batch := []*sarama.ConsumerMessage{msg1, msg2, msg3}

	// 添加批次
	tracker.AddBatch(batch)

	// 简化版本：所有批次都可以提交
	assert.True(t, tracker.CanCommitBatch(batch))

	// 添加另一个更晚的批次
	msg4 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 103}
	msg5 := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 104}
	batch2 := []*sarama.ConsumerMessage{msg4, msg5}

	tracker.AddBatch(batch2)

	// 简化版本：所有批次都可以提交
	assert.True(t, tracker.CanCommitBatch(batch))
	assert.True(t, tracker.CanCommitBatch(batch2))
}

func TestBatchOffsetTrackerConcurrency(t *testing.T) {
	// 测试简化版本的并发安全性
	tracker := NewBatchOffsetTracker()

	// 并发添加批次
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func(offset int64) {
			msg := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: offset}
			batch := []*sarama.ConsumerMessage{msg}
			tracker.AddBatch(batch)
			done <- true
		}(int64(100 + i))
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}

	// 简化版本：不再验证复杂的offset状态，只测试基本功能
	// 测试并发读取
	for i := 0; i < 5; i++ {
		go func() {
			msg := &sarama.ConsumerMessage{Topic: "test-topic", Partition: 0, Offset: 105}
			batch := []*sarama.ConsumerMessage{msg}
			// 简化版本：CanCommitBatch总是返回true
			assert.True(t, tracker.CanCommitBatch(batch))
			done <- true
		}()
	}

	for i := 0; i < 5; i++ {
		<-done
	}
}

func TestBlockChainSyncTaskBatchManagement(t *testing.T) {
	// 创建模拟的消息
	msg1 := &sarama.ConsumerMessage{
		Topic:     "test-topic",
		Partition: 0,
		Offset:    100,
	}

	msg2 := &sarama.ConsumerMessage{
		Topic:     "test-topic",
		Partition: 1,
		Offset:    200,
	}

	msg3 := &sarama.ConsumerMessage{
		Topic:     "another-topic",
		Partition: 0,
		Offset:    300,
	}

	// 创建BlockChainSyncTask实例（需要模拟依赖）
	task := &BlockChainSyncTask{
		batchTracker: NewBatchOffsetTracker(),
	}

	// 测试批次管理
	batch1 := []*sarama.ConsumerMessage{msg1, msg2}
	batch2 := []*sarama.ConsumerMessage{msg3}

	// 添加批次
	task.batchTracker.AddBatch(batch1)
	task.batchTracker.AddBatch(batch2)

	// 验证批次可以提交
	assert.True(t, task.batchTracker.CanCommitBatch(batch1))
	assert.True(t, task.batchTracker.CanCommitBatch(batch2))

	// 验证批次状态（简化架构下不需要移除测试）
	assert.True(t, task.batchTracker.CanCommitBatch(batch2))
}

func BenchmarkBatchOffsetTracker(b *testing.B) {
	tracker := NewBatchOffsetTracker()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		msg := &sarama.ConsumerMessage{
			Topic:     "test-topic",
			Partition: 0,
			Offset:    int64(i),
		}
		batch := []*sarama.ConsumerMessage{msg}

		tracker.AddBatch(batch)
		if i%100 == 0 {
			tracker.CanCommitBatch(batch)
		}
	}
}
