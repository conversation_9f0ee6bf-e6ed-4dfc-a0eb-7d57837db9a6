package transaction

import (
	"context"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	"github.com/bluele/gcache"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Model struct {
	MongoClient         *mongo.Client
	TxDocument          *mongo.Collection
	PendingTxDocument   *mongo.Collection
	PendingTxDocumentV2 *mongo.Collection
	AppConfig           *config.AppConfig
	ExecutedTxCache     gcache.Cache

	// 统计字段
	TotalInserted int64 // 总插入数量
	TotalUpdated  int64 // 总更新数量
}

func NewModel(config *config.AppConfig) (*Model, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var mongoURI string
	if strings.HasPrefix(config.MongoAddr, "mongodb://") {
		mongoURI = config.MongoAddr
	} else {
		mongoURI = "mongodb://" + config.MongoAddr
	}

	// 配置MongoDB客户端选项
	clientOptions := options.Client().ApplyURI(mongoURI).
		SetMaxPoolSize(100).                         // 最大连接池大小
		SetMinPoolSize(10).                          // 最小连接池大小
		SetMaxConnIdleTime(30 * time.Minute).        // 连接空闲时间
		SetServerSelectionTimeout(10 * time.Second). // 服务器选择超时
		SetSocketTimeout(5 * time.Minute).           // Socket超时时间
		SetConnectTimeout(10 * time.Second)          // 连接超时时间

	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		log.Errorf("get mongo client error: %v", err)
		return nil, err
	}

	// Test the connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		log.Errorf("ping mongo error: %v", err)
		return nil, err
	}

	log.Infof("MongoDB连接成功: %s", mongoURI)

	var pendingDocV1, pendingDocV2 *mongo.Collection
	if config.PendingDatabase != "" && config.PendingDatabaseCollection != "" {
		pendingDocV1 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollection)
	}
	if config.PendingDatabase != "" && config.PendingDatabaseCollectionV2 != "" {
		pendingDocV2 = mongoClient.Database(config.PendingDatabase).Collection(config.PendingDatabaseCollectionV2)
	}

	return &Model{
		AppConfig:           config,
		MongoClient:         mongoClient,
		TxDocument:          mongoClient.Database(config.Database).Collection(config.Collection),
		PendingTxDocument:   pendingDocV1,
		PendingTxDocumentV2: pendingDocV2,
	}, nil
}

func (s *Model) UpdateTransactionForkInfo(msg *model.ForkMsg) (interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	filter := bson.M{
		"b": common.HexToHash(msg.BlockHash),
		"h": common.HexToHash(msg.TxHash),
	}
	update := bson.M{"$set": bson.M{"k": msg.Fork}}

	result, err := s.TxDocument.UpdateMany(ctx, filter, update)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *Model) InsertOrUpdateTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		return nil, nil
	}

	return s.insertOrUpdateInBlockTransactionList(transactionList)
}

func (s *Model) insertOrUpdateInBlockTransactionList(transactionList []*model.Transaction) (interface{}, error) {
	if len(transactionList) == 0 {
		log.Infof("没有需要处理的交易")
		return nil, nil
	}

	// 根据数量选择不同的超时时间
	var timeout time.Duration
	if len(transactionList) == 1 {
		timeout = 10 * time.Second // 单条记录用较短超时
	} else if len(transactionList) <= 10 {
		timeout = 30 * time.Second // 小批次用中等超时
	} else {
		timeout = 2 * time.Minute // 大批次用较长超时
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	start := time.Now()
	//log.Infof("开始处理批次，交易数量: %d", len(transactionList))

	// 转换为interface{}切片（MongoDB InsertMany要求）
	documents := make([]interface{}, len(transactionList))
	for i, v := range transactionList {
		documents[i] = v
	}

	// 设置批量插入选项，禁用有序插入以跳过重复记录
	opts := options.InsertMany().SetOrdered(false)
	result, err := s.TxDocument.InsertMany(ctx, documents, opts)

	var batchInserted, batchUpdated int64

	if err != nil {
		// 批量插入报错，改为批量upsert重试
		var insertedCount int64 = 0
		if result != nil {
			insertedCount = int64(len(result.InsertedIDs))
		}
		log.Warnf("批量插入遇到错误: %v, 已插入: %d, 改为批量upsert重试", err, insertedCount)

		// 构建upsert操作
		var operations []mongo.WriteModel
		for _, v := range transactionList {
			filter := v.GetUniqueKeys()
			updateDoc := bson.M{"$set": v}
			updateModel := mongo.NewUpdateOneModel()
			updateModel.SetFilter(filter)
			updateModel.SetUpdate(updateDoc)
			updateModel.SetUpsert(true)

			operations = append(operations, updateModel)
		}

		// 执行批量upsert
		opts := options.BulkWrite().SetOrdered(false)
		upsertResult, upsertErr := s.TxDocument.BulkWrite(ctx, operations, opts)
		if upsertErr != nil {
			return nil, upsertErr
		}

		// upsert的结果统计
		batchInserted = upsertResult.UpsertedCount
		batchUpdated = upsertResult.ModifiedCount

		log.Infof("批量upsert重试完成，插入: %d, 更新: %d, 总文档: %d",
			batchInserted, batchUpdated, len(transactionList))
	} else {
		// 没有错误，所有记录都插入成功
		if result != nil {
			batchInserted = int64(len(result.InsertedIDs))
		}
		batchUpdated = 0
		log.Debugf("批量插入完全成功，插入: %d条", batchInserted)
	}

	// 更新全局统计计数器
	atomic.AddInt64(&s.TotalInserted, batchInserted)
	atomic.AddInt64(&s.TotalUpdated, batchUpdated)

	elapsed := (float64)(time.Since(start) / time.Millisecond)
	batchProcessed := batchInserted + batchUpdated
	skippedCount := int64(len(documents)) - batchProcessed

	// 性能警告
	if len(documents) == 1 && elapsed > 200 {
		log.Warnf("单条记录处理耗时过长: %.2fms，可能存在性能问题", elapsed)
	} else if len(documents) > 1 && elapsed > float64(len(documents)*50) {
		log.Warnf("批量处理效率低: %.2fms for %d records (%.2fms/record)，可能存在性能问题",
			elapsed, len(documents), elapsed/float64(len(documents)))
	}

	// 获取累计统计
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	totalProcessed := totalInserted + totalUpdated

	// 根据实际操作类型显示日志
	operationType := "批量insert"
	if err != nil {
		operationType = "批量insert+upsert重试"
	}

	log.Infof("%s完成，耗时: %.2fms, 本批次[插入: %d, 更新: %d, 跳过: %d, 处理: %d], 累计[插入: %d, 更新: %d, 总计: %d], 文档数: %d",
		operationType, elapsed, batchInserted, batchUpdated, skippedCount, batchProcessed, totalInserted, totalUpdated, totalProcessed, len(documents))

	metrics.SaveTaskMongoWriteSummaryVec.
		WithLabelValues(fmt.Sprintf("%v", s.AppConfig.BlockChainId), "normal").
		Observe(elapsed)

	// 返回统计信息的结构体
	return map[string]interface{}{
		"InsertedCount": totalInserted,
		"UpdatedCount":  totalUpdated,
		"TotalCount":    totalProcessed,
		"DocumentCount": len(documents),
		"SkippedCount":  skippedCount,
		"ElapsedMs":     elapsed,
	}, nil
}

// GetStatistics 获取统计信息
func (s *Model) GetStatistics() map[string]int64 {
	totalInserted := atomic.LoadInt64(&s.TotalInserted)
	totalUpdated := atomic.LoadInt64(&s.TotalUpdated)
	return map[string]int64{
		"TotalInserted":  totalInserted,
		"TotalUpdated":   totalUpdated,
		"TotalProcessed": totalInserted + totalUpdated,
	}
}

// ResetStatistics 重置统计信息
func (s *Model) ResetStatistics() {
	atomic.StoreInt64(&s.TotalInserted, 0)
	atomic.StoreInt64(&s.TotalUpdated, 0)
}

// Close 关闭MongoDB连接
func (s *Model) Close() error {
	if s.MongoClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		return s.MongoClient.Disconnect(ctx)
	}
	return nil
}
