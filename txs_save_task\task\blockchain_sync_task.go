package task

import (
	"crypto/tls"
	"fmt"
	"math/big"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/Shopify/sarama"
	"github.com/bluele/gcache"
	cluster "github.com/bsm/sarama-cluster"
	log "github.com/cihub/seelog"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	jsoniter "github.com/json-iterator/go"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/shopspring/decimal"
	"github.com/tokenbankteam/txs_save_task/config"
	"github.com/tokenbankteam/txs_save_task/metrics"
	"github.com/tokenbankteam/txs_save_task/model"
	"github.com/tokenbankteam/txs_save_task/model/contract"
	"github.com/tokenbankteam/txs_save_task/service"
	"github.com/tokenbankteam/txs_save_task/utils"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

// BatchOffsetTracker 用于跟踪批次处理的offset状态
type BatchOffsetTracker struct {
	partitionMinOffset map[string]int64 // key: "topic-partition", value: 该partition最小正在处理的offset
	claimedPartitions  map[string]bool  // key: "topic-partition", value: true if claimed by this consumer
	mutex              sync.RWMutex     // 保护批次状态的读写锁
}

// NewBatchOffsetTracker 创建新的批次offset跟踪器
func NewBatchOffsetTracker() *BatchOffsetTracker {
	return &BatchOffsetTracker{
		partitionMinOffset: make(map[string]int64),
		claimedPartitions:  make(map[string]bool),
	}
}

// AddBatch 添加正在处理的批次
func (b *BatchOffsetTracker) AddBatch(msgList []*sarama.ConsumerMessage) {
	if len(msgList) == 0 {
		return
	}
	batchIdentifier := fmt.Sprintf("batch (part %d, offset %d, count %d)", msgList[0].Partition, msgList[0].Offset, len(msgList))
	log.Debugf("AddBatch: Entered for %s", batchIdentifier)

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// 按partition分组，找到每个partition的最小offset
	partitionMinOffsets := make(map[string]int64)
	for _, msg := range msgList {
		key := fmt.Sprintf("%s-%d", msg.Topic, msg.Partition)
		if minOffset, exists := partitionMinOffsets[key]; !exists || msg.Offset < minOffset {
			partitionMinOffsets[key] = msg.Offset
		}
	}

	// 更新每个partition的最小offset（只有在第一个批次或更小时才更新）
	for key, minOffset := range partitionMinOffsets {
		if currentMin, exists := b.partitionMinOffset[key]; !exists || minOffset < currentMin {
			if exists {
				log.Debugf("AddBatch: For %s, updating partitionMinOffset for key %s from %d to %d", batchIdentifier, key, currentMin, minOffset)
			} else {
				log.Debugf("AddBatch: For %s, setting new partitionMinOffset for key %s to %d", batchIdentifier, key, minOffset)
			}
			b.partitionMinOffset[key] = minOffset
		} else {
			log.Debugf("AddBatch: For %s, partitionMinOffset for key %s (%d) not updated by new minOffset %d", batchIdentifier, key, currentMin, minOffset)
		}
	}
	log.Debugf("AddBatch: Exited for %s. Current partitionMinOffset state: %v", batchIdentifier, b.partitionMinOffset)
}

// CanCommitBatch 检查批次是否可以安全提交（确保没有更早的批次正在处理）
// 简化版本：由于数据按offset顺序排列，直接比较最小offset即可
func (b *BatchOffsetTracker) CanCommitBatch(msgList []*sarama.ConsumerMessage) bool {
	if len(msgList) == 0 {
		// log.Debugf("CanCommitBatch: Called with empty msgList, returning true.") // Potentially too noisy
		return true
	}
	batchIdentifier := fmt.Sprintf("batch (part %d, offset %d, count %d)", msgList[0].Partition, msgList[0].Offset, len(msgList))
	// log.Debugf("CanCommitBatch: Entered for %s. Current partitionMinOffset state: %v", batchIdentifier, b.partitionMinOffset) // Potentially too noisy if called very frequently

	b.mutex.RLock()
	defer b.mutex.RUnlock()

	// 找到当前批次每个partition的最小offset
	currentBatchMinOffsets := make(map[string]int64)
	for _, msg := range msgList {
		key := fmt.Sprintf("%s-%d", msg.Topic, msg.Partition)
		if minOffset, exists := currentBatchMinOffsets[key]; !exists || msg.Offset < minOffset {
			currentBatchMinOffsets[key] = msg.Offset
		}
	}

	// 检查每个partition：如果全局最小offset小于当前批次最小offset，则不能提交
	for key, currentMinOffset := range currentBatchMinOffsets {
		if globalMinOffset, exists := b.partitionMinOffset[key]; exists {
			// 如果全局最小offset小于当前批次最小offset，说明有更早的批次在处理
			if globalMinOffset < currentMinOffset {
				log.Infof("CanCommitBatch: FALSE for %s. PartitionKey: %s, GlobalMinOffset (%d) < CurrentBatchMinOffset (%d). Full partitionMinOffset: %v",
					batchIdentifier, key, globalMinOffset, currentMinOffset, b.partitionMinOffset)
				return false
			}
		} else {
			// partitionMinOffset[key] 不存在，意味着此分区没有更早的活动批次记录。
			// log.Debugf("CanCommitBatch: For %s, PartitionKey: %s has no entry in partitionMinOffset. Assuming committable for this partition.", batchIdentifier, key)
		}
	}

	// log.Debugf("CanCommitBatch: TRUE for %s", batchIdentifier) // Potentially too noisy for successful commits
	return true
}

// ClearPartitions 清理指定topic-partition的offset跟踪状态（用于rebalance）
func (b *BatchOffsetTracker) ClearPartitions(topicPartitions map[string][]int32) {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	// 清空旧的 claimedPartitions
	b.claimedPartitions = make(map[string]bool)
	newClaimedPartitions := make(map[string]bool)

	log.Infof("Rebalance: 开始更新 claimed partitions。传入的 topicPartitions: %v", topicPartitions)

	// 根据 ntf.Current 构建新的 claimedPartitions 集合
	if topicPartitions != nil {
		for topic, partitions := range topicPartitions {
			for _, partition := range partitions {
				key := fmt.Sprintf("%s-%d", topic, partition)
				newClaimedPartitions[key] = true
				b.claimedPartitions[key] = true // 更新 tracker 的 claimedPartitions
				log.Debugf("Rebalance: 将 partition %s 标记为 claimed", key)
			}
		}
	}

	// 清理 partitionMinOffset 中不再属于当前 consumer (即不在 newClaimedPartitions 中) 的 partition
	// 同时记录哪些 partition 被移除了
	removedFromOffsetTracker := []string{}
	for key := range b.partitionMinOffset {
		if !newClaimedPartitions[key] {
			removedFromOffsetTracker = append(removedFromOffsetTracker, key)
			delete(b.partitionMinOffset, key)
		}
	}
	if len(removedFromOffsetTracker) > 0 {
		log.Infof("Rebalance: 从 partitionMinOffset 中清理了不再 claimed 的 partitions: %v", removedFromOffsetTracker)
	}

	log.Infof("Rebalance后: 保留的 partitionMinOffset 状态: %v", b.partitionMinOffset)
	log.Infof("Rebalance后: 更新的 claimedPartitions 状态: %v", b.claimedPartitions)
}

// RemoveBatch 移除已处理完成的批次
func (b *BatchOffsetTracker) RemoveBatch(msgList []*sarama.ConsumerMessage) {
	if len(msgList) == 0 {
		return
	}
	batchIdentifier := fmt.Sprintf("batch (part %d, offset %d, count %d)", msgList[0].Partition, msgList[0].Offset, len(msgList))
	log.Debugf("RemoveBatch: Entered for %s", batchIdentifier)

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// 找到当前批次每个partition的最大offset
	currentBatchMaxOffsets := make(map[string]int64)
	for _, msg := range msgList {
		key := fmt.Sprintf("%s-%d", msg.Topic, msg.Partition)
		if maxOffset, exists := currentBatchMaxOffsets[key]; !exists || msg.Offset > maxOffset {
			currentBatchMaxOffsets[key] = msg.Offset
		}
	}

	// 更新每个partition的最小offset为当前批次的最大offset+1
	// 仅当 partition 仍被当前 consumer claim 时才更新
	for key, maxOffset := range currentBatchMaxOffsets {
		isClaimed := b.claimedPartitions[key]
		log.Debugf("RemoveBatch: For %s, processing key %s. IsClaimed: %v. MaxOffsetInBatch: %d.", batchIdentifier, key, isClaimed, maxOffset)
		if isClaimed {
			oldOffset, exists := b.partitionMinOffset[key]
			b.partitionMinOffset[key] = maxOffset + 1
			if exists {
				log.Debugf("RemoveBatch: For %s, updated partitionMinOffset for claimed key %s from %d to %d", batchIdentifier, key, oldOffset, b.partitionMinOffset[key])
			} else {
				log.Debugf("RemoveBatch: For %s, set partitionMinOffset for claimed key %s to %d (previously not set)", batchIdentifier, key, b.partitionMinOffset[key])
			}
		} else {
			log.Debugf("RemoveBatch: For %s, partition key %s is not claimed. Skipping partitionMinOffset update.", batchIdentifier, key)
		}
	}
	log.Debugf("RemoveBatch: Exited for %s. Current partitionMinOffset state: %v, claimedPartitions state: %v", batchIdentifier, b.partitionMinOffset, b.claimedPartitions)
}

type BlockChainSyncTask struct {
	TransactionService  *service.TransactionService
	AppConfig           *config.AppConfig
	AppContext          *service.AppContext
	Consumer            *cluster.Consumer
	Producer            sarama.AsyncProducer
	GWeiDecimal         decimal.Decimal
	DirectContractCache gcache.Cache

	messages []*sarama.ConsumerMessage // 消息缓冲区
	msgMutex sync.Mutex                // 保护消息缓冲区的互斥锁

	// 批次级别的offset管理
	batchTracker *BatchOffsetTracker

	// 消费统计相关字段
	statsStartTime time.Time    // 任务开始时间
	totalMessages  atomic.Int64 // 总处理消息数
	totalOffsets   atomic.Int64 // 总处理offset数
	statsTimer     *time.Timer  // 定时打印统计信息的计时器
	statsMutex     sync.RWMutex // 保护统计数据的读写锁
}

func NewBlockChainSyncTask(taskManager *Manager) (*BlockChainSyncTask, error) {
	appContext := taskManager.AppContext
	consumer := CreateConsumer(appContext)
	producer := CreateProducer(appContext)
	directContractCache := gcache.New(10240).
		LFU().
		Expiration(time.Hour * 24).
		Build()
	blockChainSyncTask := &BlockChainSyncTask{
		AppConfig:           appContext.Config,
		TransactionService:  appContext.Services["transactionService"].(*service.TransactionService),
		Consumer:            consumer,
		Producer:            *producer,
		GWeiDecimal:         decimal.RequireFromString("1000000000"),
		DirectContractCache: directContractCache,
		batchTracker:        NewBatchOffsetTracker(),
		statsStartTime:      time.Now(),
	}
	return blockChainSyncTask, nil
}
func (s *BlockChainSyncTask) startStatsTimer() {
	s.statsTimer = time.NewTimer(time.Minute)
	go func() {
		for range s.statsTimer.C {
			s.printConsumptionStats()
			s.statsTimer.Reset(time.Minute)
		}
	}()
}
func (s *BlockChainSyncTask) resetStats() {
	s.statsMutex.Lock()
	defer s.statsMutex.Unlock()

	s.statsStartTime = time.Now()
	s.totalMessages.Store(0)
	s.totalOffsets.Store(0)
}

func (s *BlockChainSyncTask) printConsumptionStats() {
	// 一次性获取所需的所有数据
	s.statsMutex.RLock()
	startTime := s.statsStartTime
	totalMessages := s.totalMessages.Load()
	totalOffsets := s.totalOffsets.Load()
	s.statsMutex.RUnlock()

	totalDuration := time.Since(startTime)
	avgMsgPerSec := float64(totalMessages) / totalDuration.Seconds()
	avgOffsetPerSec := float64(totalOffsets) / totalDuration.Seconds()

	log.Infof("Consumption statistics summary:\n"+
		"Total: Messages: %d, Offsets: %d, Time: %.2fs\n"+
		"Average Speed: %.2f msg/s, %.2f offsets/s",
		totalMessages, totalOffsets, totalDuration.Seconds(),
		avgMsgPerSec, avgOffsetPerSec)

}

func CreateConsumer(context *service.AppContext) *cluster.Consumer {
	// init (custom) config, enable errors and notifications
	kafkaConfig := cluster.NewConfig()
	kafkaConfig.Group.Return.Notifications = true
	kafkaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest
	kafkaConfig.Consumer.Offsets.CommitInterval = time.Hour * 24 * 365 * 10 //设置大时间  手动commit offset

	// init consumer
	appConfig := context.Config
	brokerList := appConfig.BrokerList
	topicList := []string{appConfig.TrxSaveTopic}
	if appConfig.PendingTrxTopic != "" {
		topicList = append(topicList, appConfig.PendingTrxTopic)
	}
	consumer, err := cluster.NewConsumer(brokerList, appConfig.TrxSaveConsumerGroup, topicList, kafkaConfig)
	if err != nil {
		log.Errorf("new consumer %v error %v", appConfig.TrxSaveConsumerGroup, err)
		return nil
	}
	return consumer
}

func createTlsConfiguration() (t *tls.Config) {
	return t
}

func CreateProducer(context *service.AppContext) *sarama.AsyncProducer {
	// For the access log, we are looking for AP semantics, with high throughput.
	// By creating batches of compressed messages, we reduce network I/O at a cost of more latency.
	config1 := sarama.NewConfig()
	tlsConfig := createTlsConfiguration()
	if tlsConfig != nil {
		config1.Net.TLS.Enable = true
		config1.Net.TLS.Config = tlsConfig
	}
	config1.Producer.RequiredAcks = sarama.WaitForLocal       // Only wait for the leader to ack
	config1.Producer.Compression = sarama.CompressionSnappy   // Compress messages
	config1.Producer.Flush.Frequency = 500 * time.Millisecond // Flush batches every 500ms

	producer, err := sarama.NewAsyncProducer(context.Config.BrokerList, config1)
	if err != nil {
		log.Errorf("Failed to start Sarama producer: %v", err)
	}

	// We will just log to STDOUT if we're not able to produce messages.
	// Note: messages will only be returned here after all retry attempts are exhausted.
	go func() {
		if producer != nil {
			for err := range producer.Errors() {
				log.Errorf("Failed to write access log entry: %v", err)
			}
		}
	}()
	return &producer
}

func (s *BlockChainSyncTask) Start() {
	defer s.Close()
	s.ready()
}

func (s *BlockChainSyncTask) Close() error {
	// 停止统计定时器
	if s.statsTimer != nil {
		s.statsTimer.Stop()
	}

	if err := s.Consumer.Close(); err != nil {
		log.Errorf("failed to shut down access log consumer cleanly, error %v", err)
	}
	return nil
}

func (s *BlockChainSyncTask) ready() {
	// trap SIGINT to trigger a shutdown.
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt, syscall.SIGTERM)

	// 启动统计定时器
	s.startStatsTimer()

	ticker := time.NewTicker(time.Duration(s.AppConfig.BatchTimeoutMs) * time.Millisecond)
	defer ticker.Stop()

	// processFunc 从 s.messages 中提取消息，返回待处理的消息列表
	// 注意：此函数期望在 s.msgMutex 已被持有的情况下调用
	processFunc := func() []*sarama.ConsumerMessage {
		var msgList []*sarama.ConsumerMessage
		batchSize := s.AppConfig.BatchSize

		if len(s.messages) >= batchSize {
			msgList = make([]*sarama.ConsumerMessage, batchSize)
			copy(msgList, s.messages[:batchSize])
			s.messages = s.messages[batchSize:]
		} else if len(s.messages) > 0 {
			msgList = make([]*sarama.ConsumerMessage, len(s.messages))
			copy(msgList, s.messages)
			s.messages = nil
			log.Infof("定时处理剩余消息: 消息数=%d", len(msgList))
		}
		return msgList
	}

	// consume notifications
	go func() {
		for ntf := range s.Consumer.Notifications() {
			marshal, _ := json.Marshal(ntf)
			log.Infof("!Rebalanced notifications: %v", string(marshal))

			// rebalance时清理不再属于当前consumer的partition状态
			if ntf.Current != nil {
				s.batchTracker.ClearPartitions(ntf.Current)
				log.Infof("Rebalance完成，已清理不相关的partition状态")
			}
		}
	}()
	// consume messages, watch signals
	for {
		select {
		case msg, ok := <-s.Consumer.Messages():
			if ok && msg != nil {
				s.msgMutex.Lock()
				s.messages = append(s.messages, msg)

				var msgsToProcess []*sarama.ConsumerMessage
				// 达到批量处理阈值时处理
				if len(s.messages) >= s.AppConfig.BatchSize {
					batchSize := s.AppConfig.BatchSize
					msgsToProcess = make([]*sarama.ConsumerMessage, batchSize)
					copy(msgsToProcess, s.messages[:batchSize])
					s.messages = s.messages[batchSize:]
				}
				s.msgMutex.Unlock() // 在启动 goroutine 前释放锁

				if len(msgsToProcess) > 0 {
					log.Infof("消息到达触发批次处理: 消息数=%d", len(msgsToProcess))
					s.retryProcess(signals, msgsToProcess) // 异步处理
				}
			} else {
				// Consumer关闭，退出循环
				log.Infof("Consumer已关闭，退出消息处理循环")
				return
			}

		case <-ticker.C:
			var msgsToProcessTicker []*sarama.ConsumerMessage
			s.msgMutex.Lock()
			if len(s.messages) > 0 { // 检查 s.messages 是否有内容应在锁内进行
				msgsToProcessTicker = processFunc() // processFunc 现在返回消息列表
			}
			s.msgMutex.Unlock() // 在启动 goroutine 前释放锁

			if len(msgsToProcessTicker) > 0 {
				// 注意：这里的日志 "定时处理剩余消息" 已被移到 processFunc 内部
				s.retryProcess(signals, msgsToProcessTicker) // 异步处理
			}

		case <-signals:
			// 收到退出信号，直接退出
			log.Infof("收到退出信号，直接退出。未处理的消息将在下次启动时重新消费")
			return
		}
	}
}

// 重试处理逻辑，返回是否收到退出信号
func (s *BlockChainSyncTask) retryProcess(signals chan os.Signal, msgList []*sarama.ConsumerMessage) {
	if len(msgList) == 0 {
		return
	}

	// 添加批次到跟踪器（只添加一次）
	s.batchTracker.AddBatch(msgList)
	defer s.batchTracker.RemoveBatch(msgList) // 确保批次完成后更新offset跟踪

	// 更新全局统计（只在首次处理时更新，避免重试时重复计算）
	s.totalMessages.Add(int64(len(msgList)))
	s.totalOffsets.Add(msgList[len(msgList)-1].Offset - msgList[0].Offset + 1)

	// 处理并写入数据库（可能重试）
	err := s.processBatch(msgList)
	if err != nil {
		log.Errorf("批次处理失败，将重试: %v", err)
		// 数据库写入失败，等待重试
		for retryCount := 1; retryCount <= 3; retryCount++ {
			select {
			case <-signals:
				return
			default:
				time.Sleep(time.Second * 3)
				log.Infof("重试批次处理 (第%d次)", retryCount)
				err = s.processBatch(msgList)
				if err == nil {
					log.Infof("批次处理重试成功，重试次数: %d", retryCount)
					break
				}
				log.Errorf("批次处理重试失败 (第%d次): %v", retryCount, err)
			}
		}

		if err != nil {
			log.Errorf("批次处理最终失败，触发panic让k8s重启pod: %v", err)
			// 在k8s环境中，panic会让pod重启，这是最安全的恢复方式
			panic(fmt.Sprintf("批次处理重试3次后仍然失败: %v", err))
		}
	}

	// 等待可以提交offset（严格顺序检查，确保数据安全）
	commitWaitTicker := time.NewTicker(time.Millisecond * 100) // Renamed to avoid conflict if outer scope has 'ticker'
	defer commitWaitTicker.Stop()

	waitCount := 0
	batchIdentifier := "N/A"
	if len(msgList) > 0 {
		batchIdentifier = fmt.Sprintf("batch (part %d, offset %d, count %d)", msgList[0].Partition, msgList[0].Offset, len(msgList))
	}

	for {
		select {
		case <-signals:
			log.Infof("retryProcess for %s: Received signal, stopping commit wait.", batchIdentifier)
			return
		case <-commitWaitTicker.C:
			if s.batchTracker.CanCommitBatch(msgList) {
				s.commitBatchOffsets(msgList)
				log.Infof("retryProcess for %s: Successfully committed batch offsets. Message count: %d. Wait iterations: %d", batchIdentifier, len(msgList), waitCount)
				return
			}
			waitCount++
			// 记录等待状态，便于调试
			if waitCount%100 == 0 { // Log more details every 100 iterations (10 seconds)
				// It's tricky to get currentBatchMinOffsets here without duplicating CanCommitBatch logic or making it return more.
				// So, we rely on CanCommitBatch's own detailed logging when it returns false.
				// We can, however, log the tracker's global state.
				s.batchTracker.mutex.RLock() // Accessing tracker state, so need a lock
				currentPartitionMinOffsetState := fmt.Sprintf("%v", s.batchTracker.partitionMinOffset)
				s.batchTracker.mutex.RUnlock()
				log.Warnf("retryProcess for %s: Still waiting to commit offsets after %d iterations (approx %ds). Current BatchOffsetTracker.partitionMinOffset: %s",
					batchIdentifier, waitCount, waitCount/10, currentPartitionMinOffsetState)
			} else if waitCount%10 == 0 { // Log basic waiting message every 10 iterations (1 second)
				log.Debugf("retryProcess for %s: Waiting to commit offsets, iteration %d. msgList count: %d.", batchIdentifier, waitCount, len(msgList))
			}
		}
	}
}

// processBatch 处理批次消息并写入数据库（不提交offset）
func (s *BlockChainSyncTask) processBatch(msgList []*sarama.ConsumerMessage) error {
	if len(msgList) == 0 {
		return nil
	}

	var (
		allTransactions []*model.Transaction
		allForkMsgs     []*model.ForkMsg
	)

	for _, msg := range msgList {
		// 处理单条消息
		trxMessageBody := model.TrxMessageBody{}
		if err := json.Unmarshal(msg.Value, &trxMessageBody); err != nil {
			return fmt.Errorf("unmarshal message error: %v, message: %s", err, string(msg.Value))
		}

		if !s.AppConfig.Pending && trxMessageBody.TransactionAndInternalTx != nil &&
			trxMessageBody.TransactionAndInternalTx.Receipt != nil &&
			trxMessageBody.TransactionAndInternalTx.Receipt.Status == 2 {
			continue
		}

		// 获取交易信息
		tx, err := s.getTransaction(&trxMessageBody)
		if err != nil {
			return fmt.Errorf("get transaction error: %v, message: %s", err, string(msg.Value))
		}

		// 收集fork消息（如果有的话）
		if trxMessageBody.TxForkMsg != nil {
			allForkMsgs = append(allForkMsgs, trxMessageBody.TxForkMsg)
		}

		if tx != nil {
			allTransactions = append(allTransactions, tx)
		}
	}

	// 批量写入数据库
	if len(allTransactions) > 0 {
		if _, err := s.TransactionService.InsertOrUpdateTransactionList(allTransactions); err != nil {
			metrics.SaveTaskErrorCounter.With(s.getMetricsLabels()).Add(float64(len(allTransactions)))
			return fmt.Errorf("failed to process batch transactions: %v", err)
		}
		metrics.SaveTaskHealthCounter.With(s.getMetricsLabels()).Add(float64(len(allTransactions)))
	}

	// 处理 fork 消息
	if len(allForkMsgs) > 0 {
		for _, forkMsg := range allForkMsgs {
			if _, err := s.TransactionService.UpdateTransactionForkInfo(forkMsg); err != nil {
				metrics.SaveTaskErrorCounter.With(s.getMetricsLabels()).Add(float64(len(allForkMsgs)))
				return fmt.Errorf("failed to process fork message: %v", err)
			}
			log.Infof("成功处理fork消息: %v", forkMsg)
		}
	}

	return nil
}

// commitBatchOffsets 为批次提交offset
func (s *BlockChainSyncTask) commitBatchOffsets(msgList []*sarama.ConsumerMessage) {
	if len(msgList) == 0 {
		return
	}

	// 按partition分组，找到每个partition的最大offset
	partitionMaxOffsets := make(map[string]*sarama.ConsumerMessage)
	for _, msg := range msgList {
		key := fmt.Sprintf("%s-%d", msg.Topic, msg.Partition)
		if existing, exists := partitionMaxOffsets[key]; !exists || msg.Offset > existing.Offset {
			partitionMaxOffsets[key] = msg
		}
	}

	// 为每个partition提交最大offset
	for _, msg := range partitionMaxOffsets {
		s.Consumer.MarkOffset(msg, "")
		log.Debugf("标记提交offset: topic=%s, partition=%d, offset=%d",
			msg.Topic, msg.Partition, msg.Offset)
	}

	// 统一提交所有标记的offset
	s.Consumer.CommitOffsets()
}

func (s *BlockChainSyncTask) getTransaction(trxMessageBody *model.TrxMessageBody) (*model.Transaction, error) {
	if trxMessageBody.BlockChain != s.AppConfig.BlockChain {
		return nil, nil
	}

	transactionAndInternalTx := trxMessageBody.TransactionAndInternalTx
	if transactionAndInternalTx == nil {
		return nil, nil
	}
	tx := transactionAndInternalTx.Transaction
	if tx == nil {
		return nil, nil
	}

	var to string
	if tx.Recipient != nil {
		to = tx.Recipient.String()
	}

	transaction := model.Transaction{
		BlockHash:    transactionAndInternalTx.BlockHash,
		BlockNumber:  int32(transactionAndInternalTx.BlockNumber),
		Timestamp:    int32(transactionAndInternalTx.Timestamp),
		Hash:         *tx.Hash,
		Nonce:        int32(tx.AccountNonce),
		Input:        tx.Payload,
		Gas:          tx.GasLimit,
		GasPrice:     tx.Price,
		BaseFee:      tx.BaseFee,
		GasTipCap:    tx.GasTipCap,
		GasFeeCap:    tx.GasFeeCap,
		ErrorMessage: transactionAndInternalTx.ErrorMessage,
	}
	decodeInput, err := hexutil.Decode(string(tx.Payload))
	if err == nil {
		transaction.Input = decodeInput
	}
	if transactionAndInternalTx.ErrorReason != "" {
		transaction.ErrorReason = transactionAndInternalTx.ParseErrReason()
	}
	receipt := transactionAndInternalTx.Receipt
	if receipt != nil {
		transaction.UsedGas = receipt.GasUsed
		transaction.TransactionIndex = int32(receipt.TransactionIndex)
		transaction.Status = int32(receipt.Status)

		if receipt.L1GasPrice != nil {
			transaction.L1GasPrice = receipt.L1GasPrice.String()
		}
		if receipt.L1GasUsed != nil {
			transaction.L1GasUsed = receipt.L1GasUsed.Int64()
		}
		if receipt.L1Fee != nil {
			transaction.L1Fee = receipt.L1Fee.String()
		}
		if receipt.FeeScalar != nil {
			transaction.FeeScalar, _ = receipt.FeeScalar.Float32()
		}
		if receipt.OperatorFeeScalar != nil {
			transaction.OperatorFeeScalar = *receipt.OperatorFeeScalar
		}
		if receipt.OperatorFeeConstant != nil {
			transaction.OperatorFeeConstant = *receipt.OperatorFeeConstant
		}
	} else {
		transaction.UsedGas = tx.GasLimit
		transaction.TransactionIndex = int32(transactionAndInternalTx.TransactionIndex)
		transaction.Status = utils.SUCCESS
	}
	var transactionList []model.Transfer

	// map[contract]protocol
	contractMap := map[string]int64{}

	s.gasFeeMonitorStatistics(transaction)
	//增加原生币
	transactionList = append(transactionList, model.Transfer{
		From:          transactionAndInternalTx.From,
		To:            common.HexToAddress(to),
		Value:         tx.Amount,
		InternalIndex: "",
		LogIndex:      -1,
	})

	if transaction.Status == utils.Failed {
		inputHex := strings.ToLower(hexutil.Encode(transaction.Input))
		//检查是否是代币转账
		if len(inputHex) > 74 && inputHex[:10] == utils.InputErc20Transfer {
			// Function: transfer(address _to, uint256 _value)
			// MethodID: 0xa9059cbb
			// 0xa9059cbb000000000000000000000000
			// [0]:00000000000000000000000075186ece18d7051afb9c1aee85170c0deda23d82
			// [1]:0000000000000000000000000000000000000000000000364db9fbe6a7902000

			var erc20Transaction model.Transfer
			erc20Transaction.From = transactionAndInternalTx.From
			erc20Transaction.To = common.HexToAddress("0x" + inputHex[34:74])
			erc20Transaction.AddrToken = common.HexToAddress(to)
			tokenValue := big.Int{}
			if err := tokenValue.UnmarshalJSON(append([]byte{'0', 'x'}, inputHex[74:]...)); err != nil {
				log.Infof("unmarshal json %v error %v", string(inputHex), err)
			}
			erc20Transaction.Value = tokenValue.String()
			erc20Transaction.LogIndex = 0
			transactionList = append(transactionList, erc20Transaction)
		} else if len(inputHex) > 138 && inputHex[:10] == utils.InputErc721TransferFrom {
			// 0x23b872dd000000000000000000000000b2df81406bacd5395cecb498b17c0617f82b30030000000000000000000000004e7cc05b62e2c38a06ba9ddb333b60cd31b712830000000000000000000000000000000000000000000000000000000002faf080
			//Function: transferFrom(address from, address to, uint256 tokenId)
			//MethodID: 0x23b872dd
			//[0]:  000000000000000000000000f5b0d10b8f75f65be17f2fa1f56c94815648e1f5
			//[1]:  0000000000000000000000003a4025a62a927016b03c74aaa2feb05f9b9dc24d
			//[2]:  0000000000000000000000000000000000000000000000000000000000007c42
			var erc721Transaction model.Transfer
			erc721Transaction.From = common.HexToAddress("0x" + inputHex[34:74])
			erc721Transaction.AddrToken = common.HexToAddress(to)
			erc721Transaction.To = common.HexToAddress("0x" + inputHex[98:138])
			tokenId := big.Int{}
			if err := tokenId.UnmarshalJSON([]byte("0x" + inputHex[138:])); err != nil {
				log.Infof("unmarshal json %v error %v", inputHex, err)
			}
			erc721Transaction.TokenId = tokenId.String()
			erc721Transaction.LogIndex = 0
			erc721Transaction.Value = "1"
			transactionList = append(transactionList, erc721Transaction)
		} else if len(inputHex) > 390 && inputHex[:10] == utils.InputErc1155TransferFrom {
			//0xf242432a0000000000000000000000006a7e144370900516b97173eebd1e9769ca095d61000000000000000000000000335b85d3282031f3fdef4e5198200ecbc8b971310000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000000
			//Function: safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes data)
			//MethodID: 0xf242432a
			//[0]:  0000000000000000000000006a7e144370900516b97173eebd1e9769ca095d61
			//[1]:  000000000000000000000000335b85d3282031f3fdef4e5198200ecbc8b97131
			//[2]:  0000000000000000000000000000000000000000000000000000000000000000
			//[3]:  0000000000000000000000000000000000000000000000000000000000000001
			//[4]:  00000000000000000000000000000000000000000000000000000000000000a0
			//[5]:  0000000000000000000000000000000000000000000000000000000000000000
			var erc1155Transaction model.Transfer
			erc1155Transaction.From = common.HexToAddress("0x" + inputHex[34:74])
			erc1155Transaction.AddrToken = common.HexToAddress(to)
			erc1155Transaction.To = common.HexToAddress("0x" + inputHex[98:138])
			tokenId := big.Int{}
			if err := tokenId.UnmarshalJSON([]byte("0x" + inputHex[138:202])); err != nil {
				log.Infof("unmarshal json %v error %v", inputHex, err)
			}
			erc1155Transaction.TokenId = tokenId.String()
			erc1155Transaction.LogIndex = 0
			amount := big.Int{}
			if err := amount.UnmarshalJSON([]byte("0x" + inputHex[202:266])); err != nil {
				log.Infof("unmarshal json %v error %v", inputHex, err)
			}
			erc1155Transaction.Value = amount.String()
			transactionList = append(transactionList, erc1155Transaction)
		}
		//if transaction.Status == utils.Failed && len(transactionList) > 1 {
		//	log.Infof("transaction %v failed,but have transfer %v", transaction.Hash, transactionList[0:])
		//}
	}

	//处理合约内部转账
	internalTxList := transactionAndInternalTx.InternalTxList
	if len(internalTxList) > 0 {
		for _, internalTx := range internalTxList {
			if internalTx.Path == "" {
				continue
			}
			if internalTx.Value == "" {
				continue
			}
			newInt := big.NewInt(0)
			newInt.SetString(internalTx.Value, 10)
			if newInt.Cmp(big.NewInt(0)) <= 0 {
				continue
			}
			internalTransaction := model.Transfer{
				From:          internalTx.From,
				To:            internalTx.To,
				Value:         internalTx.Value,
				LogIndex:      -1,
				InternalIndex: internalTx.CallType + internalTx.Path,
			}
			transactionList = append(transactionList, internalTransaction)
		}
	}
	//处理erc20
	if receipt != nil && len(receipt.Logs) > 0 {
		for _, log1 := range receipt.Logs {
			topicLen := len(log1.Topics)
			if topicLen != utils.Erc20TopicSize && topicLen != utils.Erc721TopicSize {
				continue
			}
			eventFunSign := log1.Topics[0].String()

			var receiptTx = model.Transfer{}
			receiptTx.InternalIndex = ""
			receiptTx.LogIndex = int32(log1.Index)
			receiptTx.AddrToken = log1.Address

			if eventFunSign == utils.EventSignTransfer {
				if topicLen == 1 {
					if strings.ToLower(receiptTx.AddrToken.String()) == "0x06012c8cf97bead5deae237070f9587f8e7a266d" {
						if len(log1.Data) == len("0x000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006012c8cf97bead5deae237070f9587f8e7a266d000000000000000000000000000000000000000000000000000000000012e797") {
							from := string(log1.Data[26 : 26+40])
							if !strings.HasPrefix(from, "0x") {
								from = "0x" + from
							}
							receiptTx.From = common.HexToAddress(from)
							to = string(log1.Data[66+24 : 66+24+40])
							if !strings.HasPrefix(to, "0x") {
								to = "0x" + to
							}
							receiptTx.To = common.HexToAddress(to)
							tokenId := big.Int{}
							tokenId.UnmarshalJSON([]byte("0x" + string(log1.Data[66+24+40:])))
							receiptTx.TokenId = tokenId.String()
							transactionList = append(transactionList, receiptTx)
						}
					}
				}
			} else if eventFunSign == utils.EventSignPunkTransfer {
				//https://cn.etherscan.com/tx/0xe8ab7d955ecbab17b63845401e032a0ac313b16e8833d756bbb86cfa03643cac#eventlog
				if topicLen == 3 {
					topic1 := log1.Topics[1].String()
					from := topic1[len(topic1)-40:]
					if !strings.HasPrefix(from, "0x") {
						from = "0x" + from
					}
					receiptTx.From = common.HexToAddress(from)
					topic2 := log1.Topics[2].String()
					to = topic2[len(topic2)-40:]
					if !strings.HasPrefix(to, "0x") {
						to = "0x" + to
					}
					receiptTx.To = common.HexToAddress(to)
					tokenId := big.Int{}
					tokenId.UnmarshalJSON(log1.Data)
					receiptTx.TokenId = tokenId.String()
					transactionList = append(transactionList, receiptTx)
				}
			} else if eventFunSign == utils.EventSignPunkBought {
				//https://cn.etherscan.com/tx/0xc2807451def889fcb0576fde91547ae465cafc50d030b51053144eeddcaefeb0#eventlog
				if topicLen == 4 {
					topic2 := log1.Topics[2].String()
					from := topic2[len(topic2)-40:]
					if !strings.HasPrefix(from, "0x") {
						from = "0x" + from
					}
					receiptTx.From = common.HexToAddress(from)
					topic3 := log1.Topics[3].String()
					to = topic3[len(topic3)-40:]
					if !strings.HasPrefix(to, "0x") {
						to = "0x" + to
					}
					receiptTx.To = common.HexToAddress(to)
					tokenId := big.Int{}
					tokenId.UnmarshalJSON([]byte(log1.Topics[1].Hex()))
					receiptTx.TokenId = tokenId.String()
					transactionList = append(transactionList, receiptTx)
				}
			} else if eventFunSign == utils.EventSignAssign {
				//https://cn.etherscan.com/tx/0xc2807451def889fcb0576fde91547ae465cafc50d030b51053144eeddcaefeb0#eventlog
				if topicLen == 2 {
					receiptTx.From = common.Address{}
					topic1 := log1.Topics[1].String()
					to = topic1[len(topic1)-40:]
					if !strings.HasPrefix(to, "0x") {
						to = "0x" + to
					}
					receiptTx.To = common.HexToAddress(to)
					tokenId := big.Int{}
					tokenId.UnmarshalJSON(log1.Data)
					receiptTx.TokenId = tokenId.String()
					transactionList = append(transactionList, receiptTx)
				}
			}

			tLog := types.Log{
				Address:     log1.Address,
				Topics:      log1.Topics,
				Data:        log1.Data,
				BlockNumber: log1.BlockNumber,
				TxHash:      log1.TxHash,
				TxIndex:     log1.TxIndex,
				BlockHash:   log1.BlockHash,
				Index:       log1.Index,
				Removed:     log1.Removed,
			}

			//这里需要处理一下 传过来的是str
			if len(tLog.Data) >= 2 {
				tLog.Data, _ = hexutil.Decode(string(tLog.Data))
			}
			switch eventFunSign {
			case utils.EventSignTransfer:
				{
					if topicLen == utils.Erc20TopicSize {
						topic1 := log1.Topics[1].String()
						from := topic1[len(topic1)-40:]
						if !strings.HasPrefix(from, "0x") {
							from = "0x" + from
						}
						receiptTx.From = common.HexToAddress(from)
						topic2 := log1.Topics[2].String()
						to = topic2[len(topic2)-40:]
						if !strings.HasPrefix(to, "0x") {
							to = "0x" + to
						}
						receiptTx.To = common.HexToAddress(to)
						tokenValue := big.Int{}
						err := tokenValue.UnmarshalJSON(log1.Data)
						if err != nil {
							log.Warnf("UnmarshalJSON %v err %v", log1, err.Error())
							continue
						}
						receiptTx.Value = tokenValue.String()
						contractMap[strings.ToLower(tLog.Address.String())] = utils.TokenProtocolErc20
						transactionList = append(transactionList, receiptTx)
					}
					if topicLen == utils.Erc721TopicSize {
						topic1 := log1.Topics[1].String()
						from := topic1[len(topic1)-40:]
						if !strings.HasPrefix(from, "0x") {
							from = "0x" + from
						}
						receiptTx.From = common.HexToAddress(from)
						topic2 := log1.Topics[2].String()
						to = topic2[len(topic2)-40:]
						if !strings.HasPrefix(to, "0x") {
							to = "0x" + to
						}
						receiptTx.To = common.HexToAddress(to)
						receiptTx.InternalIndex = ""
						receiptTx.LogIndex = int32(log1.Index)
						receiptTx.AddrToken = log1.Address
						tokenId := big.Int{}
						err := tokenId.UnmarshalJSON([]byte(log1.Topics[3].Hex()))
						if err != nil {
							log.Warnf("UnmarshalJSON %v err %v", log1, err.Error())
							continue
						}
						receiptTx.TokenId = tokenId.String()
						contractMap[strings.ToLower(tLog.Address.String())] = utils.TokenProtocolErc721
						transactionList = append(transactionList, receiptTx)
					}

				}
			case utils.EventTransferSingle:
				{
					erc1155Filter, err := contract.NewERC1155Filterer(common.HexToAddress(to), nil)
					if err != nil {
						log.Warnf("Get ERC1155Filterer err %v", err.Error())
						continue
					}
					singleMsg, err := erc1155Filter.ParseTransferSingle(tLog)
					if err != nil {
						log.Warnf("ParseTransferSingle  err: %v ---log %v", err.Error(), log1)
						continue
					}
					if singleMsg == nil {
						log.Warnf("Get singleMsg nil")
						continue
					}
					receiptTx.TokenId = singleMsg.Id.String()
					receiptTx.Value = singleMsg.Value.String()
					receiptTx.From = singleMsg.From
					receiptTx.To = singleMsg.To
					contractMap[strings.ToLower(tLog.Address.String())] = utils.TokenProtocolErc1155
					transactionList = append(transactionList, receiptTx)
				}
			case utils.EventTransferBatch:
				{
					erc1155Filter, err := contract.NewERC1155Filterer(receiptTx.To, nil)
					if err != nil {
						log.Warnf("Get ERC1155Filterer err %v", err.Error())
						continue
					}
					batchMsg, err := erc1155Filter.ParseTransferBatch(tLog)
					if err != nil {
						log.Warnf("ParseTransferBatch  err: %v ---log %v", err.Error(), log1)
						continue
					}
					if batchMsg == nil {
						log.Warnf("Get BatchMsg nil")
						continue
					}
					if len(batchMsg.Ids) != len(batchMsg.Values) || len(batchMsg.Values) == 0 {
						log.Warnf("BatchMsg %v err", batchMsg)
						continue
					}
					contractMap[strings.ToLower(tLog.Address.String())] = utils.TokenProtocolErc1155
					for index, id := range batchMsg.Ids {
						subTx := receiptTx
						subTx.TokenId = id.String()
						subTx.Value = batchMsg.Values[index].String()
						subTx.From = batchMsg.From
						subTx.To = batchMsg.To
						subTx.InternalIndex = fmt.Sprintf("erc1155_%d", index)
						transactionList = append(transactionList, subTx)
					}
				}

			}
		}
	}
	for i, _ := range transactionList {
		transactionList[i].AddrList = []common.Address{transactionList[i].From, transactionList[i].To}
	}
	transaction.TransferList = transactionList
	if len(transaction.TransferList) > 100 {
		log.Warnf("get large size %v transferList %v", len(transaction.TransferList), transaction.Hash)
	}
	s.directContractAddress(transaction, contractMap)

	return &transaction, nil
}

func (s *BlockChainSyncTask) setDirectCache(contractInfo *model.ContractAddressInfo) {
	s.DirectContractCache.Set(contractInfo.GenerateCacheKey(), true)
}

func (s *BlockChainSyncTask) checkDirectInCache(contractInfo *model.ContractAddressInfo) bool {
	if inCache, err := s.DirectContractCache.GetIFPresent(contractInfo.GenerateCacheKey()); err == nil && inCache != nil {
		return true
	}
	return false
}

func (s *BlockChainSyncTask) directContractAddress(transaction model.Transaction, protocolMap map[string]int64) error {
	contractAddressInfoMap := map[string]*model.ContractAddressInfo{}
	checkAndSetCacheFunc := func(cInfo *model.ContractAddressInfo) {
		if s.checkDirectInCache(cInfo) {
			return
		}
		contractAddressInfoMap[cInfo.Token] = cInfo
		//log.Infof("direct contract %v in %v", cInfo.Token, transaction.Hash.String())
		s.setDirectCache(cInfo)
	}
	for _, transfer := range transaction.TransferList {
		addrToken := strings.ToLower(transfer.AddrToken.String())
		if cInfo, ok := contractAddressInfoMap[addrToken]; ok {
			if cInfo.TokenIds != nil && transfer.TokenId != "" {
				found := false
				for _, id := range cInfo.TokenIds {
					if id == transfer.TokenId {
						found = true
						break
					}
				}
				if !found {
					cInfo.TokenIds = append(cInfo.TokenIds, transfer.TokenId)
					checkAndSetCacheFunc(cInfo)
				}
			}
			continue
		}
		contractInfo := &model.ContractAddressInfo{
			BlockChainId: s.AppConfig.BlockChainId,
			Token:        addrToken,
			Ts:           int64(transaction.Timestamp),
		}
		protocol, ok := protocolMap[addrToken]
		if !ok {
			continue
		}
		contractInfo.Protocol = protocol
		if transfer.TokenId != "" {
			contractInfo.TokenIds = []string{transfer.TokenId}
		}
		checkAndSetCacheFunc(contractInfo)
	}
	if len(contractAddressInfoMap) <= 0 {
		return nil // Modified to potentially return an error, but this part doesn't produce one.
	}
	var contractAddressInfoList []*model.ContractAddressInfo
	for _, info := range contractAddressInfoMap {
		contractAddressInfoList = append(contractAddressInfoList, info)
	}

	if len(contractAddressInfoList) == 0 {
		return nil
	}

	marshal, err := json.Marshal(contractAddressInfoList)
	if err != nil {
		log.Errorf("Failed to marshal contractAddressInfoList for producer: %v. Data: %+v", err, contractAddressInfoList)
		return fmt.Errorf("failed to marshal contractAddressInfoList: %w", err)
	}

	producerMessage := &sarama.ProducerMessage{
		Topic: s.AppConfig.WalletTokenTopic,
		Value: sarama.StringEncoder(marshal),
	}

	select {
	case s.Producer.Input() <- producerMessage:
		log.Debugf("Successfully enqueued message to Kafka topic %s for tx %s", producerMessage.Topic, transaction.Hash.String())
	case <-time.After(1 * time.Second): // 1-second timeout for enqueuing
		log.Errorf("Timeout (1s) enqueuing message to Kafka topic %s for tx %s. Message data (first 100 bytes): %s",
			producerMessage.Topic, transaction.Hash.String(), string(marshal))
		return fmt.Errorf("timeout (1s) enqueuing message to Kafka topic %s for tx %s", producerMessage.Topic, transaction.Hash.String())
	}
	return nil
}

func (s *BlockChainSyncTask) gasFeeMonitorStatistics(transaction model.Transaction) {
	if transaction.GasPrice != "" {
		newFromString, err := decimal.NewFromString(transaction.GasPrice)
		if err == nil {
			gasPriceFloat, _ := newFromString.Div(s.GWeiDecimal).Float64()
			metrics.GasPriceSummaryVec.WithLabelValues(strconv.FormatInt(s.AppConfig.BlockChainId, 10), "gas-price").Observe(gasPriceFloat)
		}
	}
	if transaction.GasTipCap != "" {
		newFromString, err := decimal.NewFromString(transaction.GasTipCap)
		if err == nil {
			gasTipCapFloat, _ := newFromString.Div(s.GWeiDecimal).Float64()
			metrics.GasPriceSummaryVec.WithLabelValues(strconv.FormatInt(s.AppConfig.BlockChainId, 10), "max-priority-fee").Observe(gasTipCapFloat)
		}
	}
	if transaction.GasFeeCap != "" {
		newFromString, err := decimal.NewFromString(transaction.GasFeeCap)
		if err == nil {
			gasFeeCapFloat, _ := newFromString.Div(s.GWeiDecimal).Float64()
			metrics.GasPriceSummaryVec.WithLabelValues(strconv.FormatInt(s.AppConfig.BlockChainId, 10), "max-fee-per-gas").Observe(gasFeeCapFloat)
		}
	}
}

func (s *BlockChainSyncTask) getMetricsLabels() prometheus.Labels {
	return map[string]string{
		"blockchain_id": fmt.Sprintf("%v", s.AppConfig.BlockChainId),
		"blockchain":    s.AppConfig.BlockChain,
	}
}
