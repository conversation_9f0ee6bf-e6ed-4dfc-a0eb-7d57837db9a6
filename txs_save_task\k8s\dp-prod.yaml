kind: Deployment
apiVersion: apps/v1
metadata:
  name: TP_TARGET
  namespace: tp-bserver
spec:
  replicas: 12
  selector:
    matchLabels:
      srv: TP_TARGET
  template:
    metadata:
      labels:
        srv: TP_TARGET
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: srv
                      operator: In
                      values:
                        - TP_TARGET
                topologyKey: "kubernetes.io/hostname"
      imagePullSecrets:
        - name: tp-hk-registry
      containers:
      - name: TP_TARGET
        image: registry.cn-hongkong.aliyuncs.com/tokenpocket/TP_TARGET:TPIMAGE_VERSION
        imagePullPolicy: "Always"
        ports:
        - containerPort: 5574
        resources:
          limits:
            cpu: '2'
            memory: 3Gi
          requests:
            cpu: '0.1'
            memory: 100Mi
        volumeMounts:
        - name: configmap-volume
          mountPath: /home/<USER>/TP_TARGET/config/config_prod.yml
          subPath: config_prod.yml
      volumes:
      - name: configmap-volume
        configMap:
          name: cm-TP_TARGET
          items:
          - key: config_prod.yml
            path: config_prod.yml
